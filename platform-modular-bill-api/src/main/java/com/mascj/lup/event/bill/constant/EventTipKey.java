package com.mascj.lup.event.bill.constant;

/**
 * <AUTHOR>
 * @date 2024/3/13 10:26
 * @describe
 */
public class EventTipKey {


    public static final String FlowViewRejectReason = "event.flow.reason_rejection";
    public static final String FlowViewPerson = "event.flow.person";
    public static final String FlowViewResult = "event.flow.check";

    /**
     * 请指定流程对象flow
     */
    public static final String NoneFlowTipKey="event.lec.flow.none.flow";


    public static final String EventPending = "event.bill.pending.tip";
    public static final String EventPendingApproval = "event.bill.pending.approval";
    public static final String EventPendingApproving = "event.bill.pending.approval.viewing";
    public static final String EventPendingApprovalAccess = "event.bill.pending.approval.access";
    public static final String EventPendingApprovalReject = "event.bill.pending.approval.reject";

    public static final String EventPendingApply ="event.bill.pending.approval.apply";
    public static final String EventBillCommonDesc = "event.bill.common.desc";
    public static final String EventPendingAutoDel = "event.bill.pending.auto.del";

    /**
     * event.bill.generateBill=已甄别
     * event.bill.generateBillNothing=未甄别
     * event.bill.push.success=推送成功
     * event.bill.push.fail=推送失败
     */
    public static final String GenerateBill ="event.bill.generateBill";
    public static final String GenerateBillNothing ="event.bill.generateBillNothing";
    public static final String BillPushSuccess ="event.bill.push.success";
    public static final String BillPushFail = "event.bill.push.fail";
    public static final String BillPushFilename = "event.bill.push.filename";

    /**
     *
     event.bill.push.log.excel.dataId=事件编号
     event.bill.push.log.excel.billNumber=工单编号
     event.bill.push.log.excel.generateBillStatus=甄别状态
     event.bill.push.log.excel.pushState=推送状态
     event.bill.push.log.excel.pushMsg=失败原因
     event.bill.push.log.excel.pushTime=推送时间
     */
    public static final String BillPushLogExcelDataId =  "event.bill.push.log.excel.dataId";
    public static final String BillPushLogExcelBillNumber =  "event.bill.push.log.excel.billNumber";
    public static final String BillPushLogExcelGenerateBillStatus =  "event.bill.push.log.excel.generateBillStatus";
    public static final String BillPushLogExcelPushState =  "event.bill.push.log.excel.pushState";
    public static final String BillPushLogExcelPushMsg =  "event.bill.push.log.excel.pushMsg";
    public static final String BillPushLogExcelPushTime =  "event.bill.push.log.excel.pushTime";

    /**
     * 请指定表单数据formData
     */
    public static final String NoneFormData = "event.lec.flow.none.formData";

    /**
     * 当前工单的流程定义processDefinitionId为空，id=
     */
    public static final String NoneProcessDefinitionId = "event.lec.flow.none.processDefinitionId";

    /**
     * 未指定参数bizCode
     */
    public static final String NoneBizCode = "event.common.tip.none.bizCode";

    /**
     * 未指定参数processDefinitionId
     */
    public static final String NoneParameterProcessDefinitionId = "event.common.tip.none.processDefinitionId";

    /**
     * 基础变量variables不能为空
     */
    public static final String EmptyVariables = "event.tip.empty.variables";

    /**
     * 网关条件{}的计算结果不能为null
     */
    public static final String NoneVar = "event.lec.gate.none.var";

    /**
     * 网关条件{}的SpEL表达式解析失败，expression
     */
    public static final String FailAnalysisVar = "event.lec.gate.none.analysis.var";

    /**
     * 计算网关条件{}时发生异常，expression
     */
    public static final String ExpAnalysisVar = "event.lec.gate.none.analysis.exp.var";

    /**
     * 请指定要节点名称activityId
     */
    public static final String NoneActivity="event.lec.none.activityPointer";
    public static final String NoneActivityDetail="event.lec.none.activity.detail";

    public static final String NoneActivityName = "event.lec.flow.none.activity.name";

    public static final String NoneActivityState ="event.lec.flow.none.activityState";

    public static final String NoneGatewayCondition="event.lec.flow.none.gatewayCondition";

    public static final String NoneConfigProvider= "event.lec.flow.configProvider";

    public static final String NoneVariableInfo = "event.lec.flow.variableInfo";
    public static final String XCETip = "event.common.xce";


    public static final String NoneCode = "event.common.tip.none.code";

    public static final String FailExportExcel = "event.common.tip.fail.export.excel";

    public static final String NonePath = "event.common.tip.none.path";

    public static final String NoneFileExist = "event.common.tip.none.file.exist";

    public static final String FailFileUpload= "event.common.tip.fail.file.upload";

    public static final String NoneMetaData ="event.tag.none.metadata";
    public static final String NoneTagName ="event.tag.none.tagName";

    public static final String NoneComparePictureUrl = "event.common.none.comparePictureUrl";
    public static final String NoneCompareAirlineTaskId = "event.common.none.compareAirlineTaskId";
    public static final String NoneCompareTile = "event.common.none.compareTile";

    /**
     * 当前数据可能已被删除，请联系管理员去回收站尝试恢复
     */
    public static final String DataMaybeDeleted = "event.common.data.maybe.deleted";

    public static final String NoneOriginalDataLogo = "event.common.none.originalDataLogo";

    public static final String NoneExistOriginalDataLogo = "event.common.none.exist.originalDataLogo";
    public static final String NoneDelOriginalDataLogo = "event.common.none.del.originalDataLogo";


    public static final String NoneReportTemplate = "event.common.none.reportTemplate";

    public static final String NoneApp = "event.common.none.app";

    public static final String NoneOrg = "event.common.none.org";

    /**
     * 查询记录为空
     */
    public static final String NoneQuery = "event.common.none.query";

    public static final String NoneTranslateObj = "event.common.none.translateObj";

    /**
     * 操作时间解析失败，time
     */

    public static final String FailOptAnalysis = "event.common.fail.optAnalysis";

    /**
     * key必填
     */
    public static final String NoneKey = "event.common.none.key";

    /**
     * 没有可以导出的数据
     */
    public static final String NoneDataExport = "event.common.none.data.export";


    /**
     * 获取XlmActivityState失败，processDefinitionId={},activityId={}
     */

    public static final String FailActivityState = "event.flow.fail.activityState";

    public static final String FailBillState = "event.flow.fail.billState";

    public static final String FailUpdate = "event.common.fail.update";

    public static final String FlowStarting ="event.flow.tip.starting";

    /**
     * 获取XlmBizCategory失败
     */
    public static final String FailFetchBizCategory = "event.flow.fail.bizCategory";

    public static final String FailStartFlow = "event.flow.fail.start";


    /**
     * 任务提交失败：processInstanceId={}, message:{}
     */

    public static final String FailCommitATask = "event.flow.fail.task.commit";

    /**
     * 任务提交失败,flowable内部异常：processInstanceId={}, message:{}
     */
    public static final String FailExpCommitTask = "event.flow.fail.task.exp";

    public static final String NoneTaskSubmit = "event.flow.none.submit.task";
    public static final String NoneTaskId = "event.flow.none.taskId";
    public static final String NoneFlowId = "event.flow.none.flowId";

    public static final String NoneFlow = "event.flow.task.none.query";

    /**
     * 当前任务已结束，无法重复提交：taskId
     */
    public static final String NoneTaskCommitRepeat = "event.flow.task.none.commit.repeat";
    public static final String EventDataCommitRepeat = "event.data.commit.repeat";


    public static final String NoneUser = "event.common.none.user";

    public static final String NoneRole = "event.common.none.role";

    public static final String NoneFlowVal = "event.common.none.flowVal";
    public static final String NoneFlowVariable = "event.common.none.flow.variable";

    public static final String FailFlowUpdate = "event.flow.fail.update";
    public static final String FailBillUpdate = "event.bill.fail.update";

    public static final String ErrorGisFormat = "event.bill.gis.format.error";

    /**
     * 没有找到定位网格，无法定位
     */
    public static final String ErrorGridLocate = "event.bill.grid.locate.error";

    /**
     * 工单网格没有变化
     */
    public static final String NoneChangeGrid = "event.bill.grid.none.change";

    /**
     * 事件不存在
     */

    public static final String NoneExistBill ="event.bill.none.exist";

    /**
     * 标注点已存在
     */
    public static final String ExistAnnotation ="event.annotation.point.exist";
    /**
     * 标注点不存在
     */
    public static final String NoneExistAnnotation ="event.annotation.point.none.exist";

    /**
     * 标注点删除失败
     */
    public static final String FailExistAnnotation = "event.annotation.point.fail.exist";

    /**
     * 标签id为空
     */
    public static final String NoneLabelId = "event.label.none.id";

    /**
     * 类型为空
     */
    public static final String NoneLabelType = "event.label.none.type";

    /**
     * 网格用户授权没有变动
     */
    public static final String NoneChangeGridRight = "event.grid.none.change.right";

    /**
     * 名称重复
     */
    public static final String DuplicationRangeName = "event.rang.name.duplication";
    /**
     * event.range.empty.file=文件为空
     */
    public static final String EmptyRangeFile = "event.range.empty.file";

    /**
     * event.range.error.file.format=文件格式有误
     */
    public static final String ErrorFileFormat = "event.range.error.file.format";
    /**
     * event.bill.file.check=未设置properties、geometry
     */
    public static final String CheckBillFile = "event.bill.file.check";

    /**
     * event.sync.task.repeat.exe=当前任务正在进行中，请稍后再试
     */
    public static final String SyncRepeatExe = "event.sync.task.repeat.exe";

    /**
     * event.sync.none.id=id不能为空
     */
    public static final String NoneSyncId = "event.sync.none.id";

    /**
     * syncState必填项
     */
    public static final String NoneSyncState ="event.sync.none.state";

    /**
     * applicationContext必填项
     */
    public static final String NoneSyncContext ="event.sync.none.context";

    /**
     * event.fail.generate.signature=生成签名失败
     */
    public static final String FailGenerateSignature ="event.fail.generate.signature";

    /**
     * event.fail.grid.del=该网格下有处置工单，不支持删除该网格
     */
    public static final String FailDelGrid ="event.fail.grid.delNoneSupport";

    /**
     * 该网格还有下级网格，不支持删除该网格
     */
    public static final String FailDelGridChild ="event.fail.grid.del.child";
    public static final String ConfigGridUser ="event.grid.config.user";

    /**
     * 请填写网格名称
     */
    public static final String NoneGridUnitName = "event.grid.none.unit.name";

    /**
     * event.grid.empty.original.logo=缺少参数，原始数据的唯一标识不能为空字符串
     */
    public static final String EmptyOriginalLogo = "event.grid.empty.original.logo";

    public static final String NoneTip = "event.common.empty";
    public static final String RequiredTip = "event.common.required";

    public static final String MarkPointTip = "event.common.marker.point";

    public static final String UnitMeter = "event.common.distance.unit.meter";
    public static final String UnitKilometer = "event.common.distance.unit.kilometer";
    /**
     * 星期一：Monday（英文）, วันจันทร์（泰文）
     * 星期二：Tuesday（英文）, วันอังคาร（泰文）
     * 星期三：Wednesday（英文）, วันพุธ（泰文）
     * 星期四：Thursday（英文）, วันพฤหัสบดี（泰文）
     * 星期五：Friday（英文）, วันศุกร์（泰文）
     * 星期六：Saturday（英文）, วันเสาร์（泰文）
     * 星期日：Sunday（英文）, วันอาทิตย์（泰文）
     */
    public static final String DateMonday ="event.common.date.monday";
    public static final String DateTuesday ="event.common.date.tuesday";
    public static final String DateWednesday ="event.common.date.wednesday";
    public static final String DateThursday ="event.common.date.thursday";
    public static final String DateFriday ="event.common.date.friday";
    public static final String DateSaturday ="event.common.date.saturday";
    public static final String DateSunday ="event.common.date.sunday";

    /**
     * 一月（January）：มกราคม（Makha Bucha Day）2.
     * 二月（February）：กุมภาพันธ์（Kumphaphan）3.
     * 三月（March）：มีนาคม（Mēnamak）4.
     * 四月（April）：เมษายน（Mēsa）5.
     * 五月（May）：พฤษภาคม（Phrueksaphakhom）6.
     * 六月（June）：มิถุนายน（Mitthunāy）7.
     * 七月（July）：กรกฎาคม（Krasūgaprom）8.
     * 八月（August）：สิงหาคม（Singhābhorn）9.
     * 九月（September）：กันยายน（Kanyān）10.
     * 十月（October）：ตุลาคม（Tulākom）11.
     * 十一月（November）：พฤศจิกายน（Phasitthakhom）12.
     * 十二月（December）：ธันวาคม（Tanwākhom）
     */
    public static final String DateJanuary ="event.common.date.january";
    public static final String DateFebruary ="event.common.date.february";
    public static final String DateMarch ="event.common.date.march";
    public static final String DateApril ="event.common.date.april";
    public static final String DateMay ="event.common.date.may";
    public static final String DateJune ="event.common.date.june";
    public static final String DateJuly ="event.common.date.july";
    public static final String DateAugust ="event.common.date.august";
    public static final String DateSeptember ="event.common.date.september";
    public static final String DateOctober ="event.common.date.october";
    public static final String DateNovember ="event.common.date.november";
    public static final String DateDecember ="event.common.date.december";

    /**
     * event.flow.comment.client=操作端
     */
    public static final String CommentOperationClient="event.flow.comment.client";

    /**
     * event.flow.comment.remark=备注
     */
    public static final String CommentRemark="event.flow.comment.remark";

    /**
     * event.flow.comment.source=材料描述
     */
    public static final String CommentSourceDescribe="event.flow.comment.source";

    public static final String CommonReportFieldEventName="event.common.report.field.eventName";
    public static final String CommonReportFieldCreateTime="event.common.report.field.createTime";

    public static final String CommonReportFieldEventTag="event.common.report.field.eventTag";

    public static final String CommonReportFieldArea="event.common.report.field.area";

    public static final String CommonReportGridUnit="event.common.report.field.gridUnit";

    public static final String CommonReportEventOrigin="event.common.report.field.eventOrigin";
    public static final String CommonReportSquare="event.common.report.field.square";

    public static final String CommonReportEventStatus="event.common.report.field.eventStatus";

    public static final String CommonReportEventLng="event.common.report.field.eventLng";
    public static final String CommonReportEventLat="event.common.report.field.eventLat";
    public static final String  CommonReportLocationAddress="event.common.report.field.locationAddress";
    public static final String  CommonReportHandleTyeName="event.common.report.field.handleTyeName";

    public static final String CommonReportHasEventName="event.common.report.field.hasEvent";
    public static final String CommonReportPatrolContent="event.common.report.field.patrolContent";
    public static final String CommonReportEventUrlLink = "event.common.report.field.eventUrl";

    public static final String CommonReportEventUrlLinkDisplayName = "event.common.report.field.eventUrlDisplayName";

    public static final String CommonReportFileName = "event.common.report.filename";
    public static final String UnknownAreaName = "event.common.unknownAreaName";

    public static final String CommonSuccess = "event.common.return.success";
    public static final String CommonDealComplete = "event.flow.deal_complete";

}
